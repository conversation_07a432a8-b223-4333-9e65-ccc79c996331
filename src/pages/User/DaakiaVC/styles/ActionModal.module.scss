$modal-background-color: #1e1e1e;
$allow-button-color: #3B60E4;
$font: "Inter", sans-serif;
$header-color: #fff;

.actionModal {
  width: 20rem !important;
  :global(.ant-modal-body) {
    padding-top: 1.5rem;
    padding-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }
  :global(.ant-modal-content) {
    border-radius: 1rem;
    background-color: $modal-background-color;
    box-shadow: 0 0 5px 0 rgba(193, 193, 193, 0.1);
  }
  :global(.ant-modal-close) {
    color: #fff;
    top: 0.8rem;
    right: 0.3rem;
  }
  h1, h2, h3, h4, h5, h6 {
    color: $header-color;
  }
  &<PERSON><PERSON> {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: $allow-button-color;
    border: none;
    cursor: pointer;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    color: $header-color;
    font-size: 0.8rem;
    font-weight: 600;
    font-family: $font;
  }
}
