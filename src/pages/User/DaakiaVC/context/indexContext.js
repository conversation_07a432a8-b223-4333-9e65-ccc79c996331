import React, { createContext, useContext, useState, useMemo, useRef } from 'react'

const VirtualBackgroundContext = createContext()
export function VirtualBackgroundProvider({ children }) {
  const [currentEffect, setCurrentEffect] = useState(null)
  const [localVirtualBackgrounds, setLocalVirtualBackgrounds] = useState([]) // Store local VBGs when user has no token
  const isApplyingEffect = useRef(false) // Prevent multiple simultaneous applications

  const contextValue = useMemo(() => ({
    currentEffect,
    setCurrentEffect,
    localVirtualBackgrounds,
    setLocalVirtualBackgrounds,
    isApplyingEffect,
  }), [currentEffect, localVirtualBackgrounds])

  return (
    <VirtualBackgroundContext.Provider value={contextValue}>
      {children}
    </VirtualBackgroundContext.Provider>
  )
}

const NoiseSuppressionContext = createContext()
export function NoiseSuppressionProvider({ children }) {
  const [isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled] = useState(false)

  const contextValue = useMemo(() => ({
    isNoiseSuppressionEnabled,
    setIsNoiseSuppressionEnabled,
  }), [isNoiseSuppressionEnabled])

  return (
    <NoiseSuppressionContext.Provider value={contextValue}>
      {children}
    </NoiseSuppressionContext.Provider>
  )
}

const AutomaticPiPContext = createContext()
export function AutomaticPiPProvider({ children }) {
  const [isAutomaticPiPEnabled, setIsAutomaticPiPEnabled] = useState(true)
  const [hasAskedPermission, setHasAskedPermission] = useState(false)
  const [isAutomaticPiPSupported, setIsAutomaticPiPSupported] = useState(false)
  const [automaticPiPFunctions, setAutomaticPiPFunctions] = useState({})

  const contextValue = useMemo(() => ({
    isAutomaticPiPEnabled,
    setIsAutomaticPiPEnabled,
    hasAskedPermission,
    setHasAskedPermission,
    isAutomaticPiPSupported,
    setIsAutomaticPiPSupported,
    automaticPiPFunctions,
    setAutomaticPiPFunctions,
  }), [isAutomaticPiPEnabled, hasAskedPermission, isAutomaticPiPSupported, automaticPiPFunctions])

  return (
    <AutomaticPiPContext.Provider value={contextValue}>
      {children}
    </AutomaticPiPContext.Provider>
  )
}

export function IndexProvider({ children }) {
  return (
    <VirtualBackgroundProvider>
      <NoiseSuppressionProvider>
        <AutomaticPiPProvider>
          {children}
        </AutomaticPiPProvider>
      </NoiseSuppressionProvider>
    </VirtualBackgroundProvider>
  )
}

export function useVirtualBackground() {
  const context = useContext(VirtualBackgroundContext)
  if (!context) throw new Error('useVirtualBackground must be used within VirtualBackgroundProvider')
  return context
}

export function useNoiseSuppressionContext() {
  const context = useContext(NoiseSuppressionContext)
  if (!context) throw new Error('useNoiseSuppressionContext must be used within NoiseSuppressionProvider')
  return context
}

export function useAutomaticPiPContext() {
  const context = useContext(AutomaticPiPContext)
  if (!context) throw new Error('useAutomaticPiPContext must be used within AutomaticPiPProvider')
  return context
}

export default VirtualBackgroundContext