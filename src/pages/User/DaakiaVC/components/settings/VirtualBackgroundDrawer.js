import React, { useEffect, useState } from "react";
import { datadogLogs } from "@datadog/browser-logs";
import { Tooltip } from "antd";
import axios from "axios";
import { DeleteOutlined } from "@ant-design/icons";
import { Track } from "livekit-client";
import { BackgroundBlur, VirtualBackground } from "@livekit/track-processors";
import * as routes from "../../API/Endpoints/routes";
import {
  toggleBlur,
  toggleVirtualBackground,
  noEffect,
} from "../../utils/virtualBackground";
import { ReactComponent as PlusIcon } from "./icons/Plus.svg";
import { constants, virtualBackground } from "../../utils/constants";
import { getLocalStorageToken, parseMetadata } from "../../utils/helper";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import SideDrawer from "../SideDrawer";
import "../../styles/VirtualBackgroundDrawer.scss";
import "../../styles/index.scss";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";
import { useVirtualBackground } from "../../context/indexContext";

export function VirtualBackgroundDrawer({
  isVBDrawerOpen,
  // setIsVBDrawerOpen,
  room,
  isWhiteboardOpen,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const {setCurrentEffect, localVirtualBackgrounds, setLocalVirtualBackgrounds } = useVirtualBackground();
  const [backgrounds, setBackgrounds] = useState([
    {
      heading: "Custom",
      effects: [
        {
          label: "Upload",
          icon: <PlusIcon />,
          value: "Upload",
        },
      ],
    },
    ...virtualBackground,
  ]);
  const { saasHostToken, isSaaS } = useSaasHelpers();

  const fetchVirtualBackgrounds = async () => {
    try {
      const response = await SettingsMenuServices.getVirtualBackground(
        room?.localParticipant?.participantInfo,
        saasHostToken,
      );
      if (response.success === 1) {
        const { data } = response;
        const custom = backgrounds.find(
          (category) => category.heading === "Custom"
        );
        data.forEach((item, index) => {
          // Check if virtual_background_id is not null and URL is not present
          if (item.id !== null) {
            const isUrlPresent = custom.effects.some(
              (effect) => effect.icon === item.url
            );
            if (!isUrlPresent) {
              custom.effects.unshift({
                label: `Custom ${index + 1}`,
                icon: item.url,
                value: `CT_${index + 1}`,
                id: item.id, // ensure id is included
              });
            }
          }
        });

        // Update the state with the new backgrounds
        // in backgrounds array[0] is custom background and in that effects array is there a
        setBackgrounds([...backgrounds]);
        // console.log("Backgrounds", backgrounds);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.error("Error fetching virtual backgrounds", error);
    }
  };

  useEffect(() => {
    if (room?.state === "connected") {
      if (parseMetadata(room?.localParticipant.metadata)?.role_name === "moderator") {
        fetchVirtualBackgrounds();
      } else if (localVirtualBackgrounds.length > 0) {
        // If not moderator, load from context state
        const customCategory = backgrounds.find(cat => cat.heading === "Custom");
        if (customCategory) {
          localVirtualBackgrounds.forEach(localVBG => {
            const isAlreadyPresent = customCategory.effects.some(effect => effect.id === localVBG.id);
            if (!isAlreadyPresent) {
              customCategory.effects.unshift(localVBG);
            }
          });
          setBackgrounds([...backgrounds]);
        }
      }
    }
  }, []);

  const handleUpload = async () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];

      if (file) {
        // Convert file to data URL instead of blob URL for CSP compatibility
        const fileURL = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target.result);
          reader.readAsDataURL(file);
        });
        if (
          parseMetadata(room?.localParticipant.metadata)?.role_name === "moderator"
        ) {
          const formData = new FormData();
          formData.append("image", file);
          // Use saasHostToken for SaaS meetings, otherwise use getLocalStorageToken()
          const authToken = isSaaS ? saasHostToken : getLocalStorageToken();
          const response = await axios.post(
            `${constants.STAG_BASE_URL}${routes.Endpoints.set_virtual_background.url}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `${authToken}`,
              },
            }
          );
          if (response.data.success === 0) {
            setToastNotification("Something went wrong while upload. Please try again later.");
            setToastStatus("error");
            setShowToast(true);
            datadogLogs.logger.error("Error in uploading virtual background",{
              response,
              payload:file,
              endpoint: routes.Endpoints.set_virtual_background.url,
              user:room?.localParticipant?.participantInfo
            })
            return;
          }else{
            setToastNotification("Virtual background uploaded successfully.");
            setToastStatus("success");
            setShowToast(true);
            datadogLogs.logger.info("Success in uploading virtual background",{
              response,
              payload:file,
              endpoint: routes.Endpoints.set_virtual_background.url,
              user:room?.localParticipant?.participantInfo
            })
          }

          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift({
                label: file.name,
                icon: fileURL,
                value: `CT_${category.effects.length + 1}`,
                id: response.data.data?.id,
              });
            }
          }
        }else {
          // Not moderator - add to context state
          const newVBG = {
            label: file.name,
            icon: fileURL,
            value: `CT_${Date.now()}`,
            id: `local_${Date.now()}`,
          };
          setLocalVirtualBackgrounds(prev => [newVBG, ...prev]);

          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift(newVBG);
            }
          }
        }
        // Apply the virtual background using the uploaded image
        // toggleVirtualBackground(room, fileURL);
        setBackgrounds([...backgrounds]);
        // Optionally, you can clean up the URL after use
        // URL.revokeObjectURL(fileURL);
      } else {
        setToastNotification("No file selected");
        setToastStatus("error");
        setShowToast(true);
        // console.log("No file selected");
      }
    };

    fileInput.click(); // Programmatically click the hidden input to open the file dialog
  };

  // Smooth transition function to avoid flickering
  const applySmoothEffect = async (effectType, value) => {
    if (!room) return;

    try {
      const camTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Camera);
      if (!camTrackPublication) throw new Error("Camera track not found");

      const camTrack = camTrackPublication.track;
      if (!camTrack) throw new Error("Camera track is not available");

      let newProcessor = null;

      // Create the new processor first
      if (effectType === 'blur') {
        newProcessor = BackgroundBlur(value, { delegate: "GPU" });
      } else if (effectType === 'background') {
        newProcessor = VirtualBackground(value);
      }

      // Apply the new processor directly - this will replace the old one smoothly
      if (newProcessor) {
        await camTrack.setProcessor(newProcessor);
      } else if (camTrack.getProcessor()) {
        // For no effect, stop the processor
        await camTrack.stopProcessor();
      }
    } catch (error) {
      console.error("Error applying smooth effect:", error);
      // Fallback to original functions if smooth transition fails
      if (effectType === 'blur') {
        await toggleBlur(room, value);
      } else if (effectType === 'background') {
        await toggleVirtualBackground(room, value);
      } else {
        await noEffect(room);
      }
    }
  };

  const handleDelete = async (virtualBackgroundId) => {
    if (parseMetadata(room?.localParticipant.metadata)?.role_name === "moderator") {
      // Moderator - use API
      virtualBackgroundId = parseInt(virtualBackgroundId);
      try {
        const authToken = isSaaS ? saasHostToken : getLocalStorageToken();
        await axios.delete(
          `${constants.STAG_BASE_URL}${routes.Endpoints.delete_virtual_background.url}`,
          {
            data: {
              virtual_background_id: virtualBackgroundId,
            },
            headers: {
              Authorization: `${authToken}`,
            },
          }
        );
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        return;
      }
    } else if (typeof virtualBackgroundId === 'string' && virtualBackgroundId.startsWith('local_')) {
      // Not moderator - remove from context state
      setLocalVirtualBackgrounds(prev => prev.filter(vbg => vbg.id !== virtualBackgroundId));
    }

    // Update local backgrounds state
    for (const category of backgrounds) {
      if (category.heading === "Custom") {
        category.effects = category.effects.filter(
          (effect) => effect.id !== virtualBackgroundId
        );
      }
    }
    setBackgrounds([...backgrounds]);
    await applySmoothEffect('none');
    if (setCurrentEffect) {
      setCurrentEffect(null);
    }
  };

  return (
    <SideDrawer
      show={isVBDrawerOpen}
      // setShow={setIsVBDrawerOpen}
      title="Backgrounds"
      isWhiteboardOpen={isWhiteboardOpen}
      className="vg-drawer"
    >
      {backgrounds.map((category) => (
        <div key={category.heading} className="vg-category-container">
          <div className="vg-heading primary-font">
            <span>{category.heading}</span>
          </div>
          <div className="vg-category-effects">
            {category.effects.map((effect) => (
              <div key={effect.label} className="vg-category-effect">
                {(category.heading === "Effects" ||
                  category.heading === "Custom") &&
                effect.icon ? (
                  <Tooltip
                    placement="top"
                    title={effect.label}
                    color={"#2db7f5"}
                  >
                    <div
                      className="vg-card"
                      onClick={async () => {
                        if (effect.value === 0) {
                          // await applySmoothEffect('none'); // Commented out - let VideoConference handle it
                          // Step 3: Set the state when no effect is selected
                          if (setCurrentEffect) {
                            setCurrentEffect(null);
                          }
                        } else if (effect.value === "Upload") {
                          handleUpload();
                        } else if (
                          typeof effect.value === "string" &&
                          effect.value?.startsWith("CT_")
                        ) {
                          // await applySmoothEffect('background', effect.icon); // Commented out - let VideoConference handle it
                          // console.log('🎨 Virtual background applied from drawer:', effect.icon);
                          // Step 3: Set the state when background effect is selected
                          if (setCurrentEffect) {
                            setCurrentEffect({ type: 'background', value: effect.icon });
                          }
                        } else if (setCurrentEffect) {
                          // await applySmoothEffect('blur', effect.value); // Commented out - let VideoConference handle it
                          // console.log('🌫️ Blur effect applied from drawer:', effect.value);
                          // Step 3: Set the state when blur effect is selected
                          setCurrentEffect({ type: 'blur', value: effect.value });
                        }
                      }}
                    >
                      {(category.heading === "Effects" && effect.icon) ||
                      effect.value === "Upload" ? (
                        <div className="vg-card-image">{effect.icon}</div>
                      ) : (
                        <div>
                          <img
                            alt={effect.label}
                            src={effect.icon}
                            className={`vg-card-image ${effect.id}`}
                          />
                          <span
                            onClick={(e) => {
                              handleDelete(effect.id);
                              e.stopPropagation();
                            }}
                            className="delete-bg"
                          >
                            <DeleteOutlined />
                          </span>
                        </div>
                      )}
                    </div>
                  </Tooltip>
                ) : (
                  <div
                    className="vg-card"
                    onClick={async () => {
                      // await applySmoothEffect('background', effect.value); // Commented out - let VideoConference handle it
                      // console.log('🖼️ Virtual background applied from drawer:', effect.value);
                      // Step 3: Set the state when background effect is selected
                      if (setCurrentEffect) {
                        setCurrentEffect({ type: 'background', value: effect.value });
                      }
                    }}
                    >
                    <img
                      alt={effect.label}
                      src={effect.icon ? effect.icon : effect.value}
                      className="vg-card-image"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </SideDrawer>
  );
}
