/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo, useRef, useState } from "react";
import Joyride from 'react-joyride';
import SideDrawer from "../SideDrawer";
import "./LiveCaptions.scss";
import { DataReceivedEvent } from "../../utils/constants";
import { parseMetadata } from "../../utils/helper";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import LiveCaptionsTitle from "./LiveCaptionsTitle";
import LiveCaptionsMessages from "./LiveCaptionsMessages";
import LanguageSelection from "./LanguageSelection";
import {
  logger,
  modalNotification,
} from "../../../../../utils";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";

export default function LiveCaptionsDrawer({
  isLiveCaptionsDrawerOpen,
  setIsLiveCaptionsDrawerOpen,
  remoteParticipants,
  localParticipant,
  liveCaptionData,
  meetingDetails,
  livecaptionsobject,
  setlivecaptionsobject,
  setfinalcaptions,
  finalcaptions,
  isWhiteboardOpen,
  id,
  translationDetails,
  setTranslationDetails,
  finalTranslatedCaptions,
  setFinalTranslatedCaptions,
  meetingFeatures,
  coHostToken,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  // Live captions tour state
  const [liveCapTourState, setLiveCapTourState] = useState({
    run: false,
    sidebarOpen: false,
    stepIndex: 0,
    steps: [],
  });

  // Derive host/cohost status from localParticipant metadata
  const isHost = parseMetadata(localParticipant.metadata)?.role_name === "moderator";
  const isCoHost = parseMetadata(localParticipant.metadata)?.role_name === "cohost";

  const tourCompleted = JSON.parse(localStorage.getItem(`lk-user-choices`))?.tourCompleted || false;

  const { saasHostToken } = useSaasHelpers();

  const setTourCompleted = () => {
    const lkUserChoices = JSON.parse(localStorage.getItem(`lk-user-choices`)) || {};
    lkUserChoices.tourCompleted = true;
    localStorage.setItem(`lk-user-choices`, JSON.stringify(lkUserChoices));
  }

  // Tour callback handler
  const handleJoyrideCallback = (data) => {
    const { action, index, status, type } = data;
    if (status === 'finished' || status === 'skipped') {
      setTourCompleted();
      setLiveCapTourState(prev => ({ ...prev, run: false, stepIndex: 0 }));
    } else if (type === 'step:after' || type === 'target_not_found') {
      const nextStepIndex = index + (action === 'prev' ? -1 : 1);
      setLiveCapTourState(prev => ({ ...prev, stepIndex: nextStepIndex }));
    }
  };

  // Setup tour steps based on user role and language selection
  useEffect(() => {
    if (!tourCompleted) {
      if(isHost || isCoHost){
        if(livecaptionsobject.isLanguageSelected){
          setLiveCapTourState(prev => ({
            ...prev,
            run: true,
            steps: [
              {
                content: 'This is the Live Captions drawer.',
                target: '.live-captions',
                title: 'Live Captions Drawer',
                disableBeacon: true,
                spotlightClicks: false,
                placement: 'left',
              },
              {
                content: 'Here you will be able to see the captions in the selected language.',
                target: '.live-cap',
                title: 'Live Captions',
                spotlightClicks: false,
                placement: 'left',
              },
            ]
          }));
        } else {
          setLiveCapTourState(prev => ({
            ...prev,
            run: true,
            steps: [
              {
                content: 'This is the Live Captions drawer.',
                target: '.live-captions',
                title: 'Live Captions Drawer',
                disableBeacon: true,
                spotlightClicks: false,
                placement: 'left',
              },
              {
                content: 'This is the Search Bar where you can search for your preferred language for the captions.',
                target: '.language-search-box',
                title: 'Search Language',
                spotlightClicks: true,
                placement: 'left',
              },
              {
                content: 'Select the language you want to see the captions in.',
                target: '.language-options',
                title: 'Select Language',
                spotlightClicks: true,
                placement: 'left',
              },
              {
                content: 'Here you will be able to see the captions in the selected language.',
                target: '.live-cap',
                title: 'Live Captions',
                spotlightClicks: false,
                placement: 'left',
              },
            ],
          }));
        }
      } else {
        // If the user is not a host or co-host
        setLiveCapTourState(prev => ({
          ...prev,
          run: true,
          steps: [
            {
              content: 'This is the Live Captions drawer.',
              target: '.live-captions',
              title: 'Live Captions Drawer',
              disableBeacon: true,
              spotlightClicks: false,
              placement: 'left',
            },
            {
              content: 'By clicking here you will be able to select your preferred language for live transcription.',
              target: '.live-cap-settings-icon',
              title: 'Live Transcription Language',
              disableBeacon: true,
              spotlightClicks: true,
              placement: 'left',
            },
            {
              content: 'Here you can search for your preferred language to see the captions in.',
              target: '.language-search-box',
              title: 'Live Transcription Language Search',
              disableBeacon: true,
              spotlightClicks: true,
              placement: 'left',
              scrollToSteps: true,
              event: 'scroll',
            },
            {
              content: 'Select the language you want to see the captions in.',
              target: '.language-options',
              title: 'Select Language',
              spotlightClicks: true,
              disableBeacon: true,
              placement: 'left',
            },
            {
              content: 'Here you will be able to see the captions in the selected language.',
              target: '.live-cap',
              title: 'Live Captions',
              spotlightClicks: false,
              disableBeacon: true,
              placement: 'left',
            },
          ]
        }));
      }
    }
  }, [liveCapTourState.sidebarOpen, isHost, isCoHost, livecaptionsobject.isLanguageSelected, tourCompleted]);

  // Control tour visibility based on drawer open/close
  useEffect(() => {
    if(isLiveCaptionsDrawerOpen && !tourCompleted) {
      setLiveCapTourState(prev => ({
        ...prev,
        run: true,
        sidebarOpen: true,
      }));
    } else {
      setLiveCapTourState(prev => ({
        ...prev,
        run: false,
        sidebarOpen: false,
      }));
    }
  }, [isLiveCaptionsDrawerOpen, tourCompleted]);

  // Function to handle language selection
  const handleLanguageSelection = async (langId, language) => {
    const newLiveCaptionsObject = {
      ...livecaptionsobject,
      langCode: langId,
      isLanguageSelected: true,
    };

    try {
      const response = await SettingsMenuServices.setLiveTranscriptionDetail(
        id,
        langId,
        language,
        true,
        localParticipant?.participantInfo,
        saasHostToken || coHostToken
      );
      if (response.success === 0) {
        setToastNotification("Error setting live transcription detail");
        setToastStatus("error");
        setShowToast(true);
        throw new Error("Error setting live transcription detail");
      } else if (response.success === 1) {
        setToastNotification(`Live transcription language set to ${language}`);
        setToastStatus("success");
        setShowToast(true);
        setlivecaptionsobject(newLiveCaptionsObject);
      }
      const encoder = new TextEncoder();
      const data = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.SHOW_LIVECAPTION,
          // livecaptionsdata: newLiveCaptionsObject,
          liveCaptionsData: {
            ...newLiveCaptionsObject,
            source_lang: langId, // Include source_lang
          },
        })
      );
      await localParticipant.publishData(data, {
        reliable: true,
      });
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.error("Error stringifying object:", error);
    }
  };

  // Function to generate timestamp
  const timeStamp = useMemo(() => {
    return () => {
      const now = new Date();
      let hours = now.getHours();
      const minutes = now.getMinutes().toString().padStart(2, "0");
      const ampm = hours >= 12 ? "PM" : "AM";
      hours %= 12;
      hours = hours.toString().padStart(2, "0");
      return `${hours}:${minutes} ${ampm}`;
    };
  }, []);

  useEffect(() => {
    // Only update if the source_lang actually changed
    if (livecaptionsobject.langCode !== translationDetails.source_lang) {
      setTranslationDetails(prev => ({
        ...prev,
        source_lang: livecaptionsobject.langCode,
      }));
    }
  }, [livecaptionsobject.langCode, translationDetails.source_lang]);

  const fetchTranslatedText = async (textData, sourceLang, targetLang) => {
    try {
      if (!textData || !targetLang) {
        // console.log("Provide both text data and target language.");
        return null;
      }

      let bodyData = {
        meeting_uid: id,
        text: textData,
        source_language: sourceLang,
        target_language: targetLang,
      };

      const res = await SettingsMenuServices.getTextTranslationDetail(
        bodyData,
        saasHostToken
      );

      const { success, data } = res;

      if (success === 1) {
        return {
          translatedText: data?.translatedText,
        };
      } else {
        setToastNotification("Translation failed.");
        setShowToast(true);
        setToastStatus("error");
        // console.error("Translation failed.");
        return null;
      }
    } catch (error) {
      setShowToast(true);
      setToastNotification(error.message);
      setToastStatus("error");
      logger(error);
      throw error;
    }
  };

  const getTranslatedData = async (textData) => {
    const lastCaption = finalcaptions[finalcaptions.length - 1];

    try {
      const translationResult = await fetchTranslatedText(
        textData,
        translationDetails.source_lang,
        translationDetails.target_lang,
      );

      if (translationResult) {
        const { translatedText } = translationResult;

        setFinalTranslatedCaptions((prevCaptions) => [
          ...prevCaptions,
          {
            name: lastCaption?.name,
            time: lastCaption?.time,
            text: translatedText,
            targetLang: translationDetails.target_lang,
            sourceLang: translationDetails.source_lang,
          },
        ]);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  useEffect(() => {
    const lastCaption = finalcaptions[finalcaptions.length - 1];
    if(translationDetails.target_lang !== ""){
      getTranslatedData(lastCaption?.text);
    }
  }, [finalcaptions]);

  const translateSelctedText = async (text) => {
    const findTextIndex = finalTranslatedCaptions.findIndex((caption) => caption.text === text);
    const selectedText = finalTranslatedCaptions[findTextIndex];
    const extractedText = selectedText.text;
    const sourceLanguage = selectedText.targetLang || selectedText.sourceLang;
    const targetLanguage = translationDetails.target_lang;

    try {
      const translationResult = await fetchTranslatedText(
        extractedText,
        sourceLanguage,
        targetLanguage
      );

      if (translationResult) {
        const { translatedText } = translationResult;

        // Create a new object instead of mutating the existing one
        const updatedCaption = {
          ...selectedText,
          text: translatedText,
          targetLang: targetLanguage,
          sourceLang: sourceLanguage,
        };

        setFinalTranslatedCaptions((prevCaptions) => [
          ...prevCaptions.slice(0, findTextIndex),
          updatedCaption,
          ...prevCaptions.slice(findTextIndex + 1),
        ]);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  console.log("finalcaptions", finalcaptions);

  return (
    <SideDrawer
      className="live-captions"
      isWhiteboardOpen={isWhiteboardOpen}
      title={
        <LiveCaptionsTitle
          isLanguageSelected={livecaptionsobject.isLanguageSelected}
          handleLanguageSelection={handleLanguageSelection}
          isHost={
            parseMetadata(localParticipant.metadata)?.role_name === "moderator"
          }
          isCoHost={
            parseMetadata(localParticipant.metadata)?.role_name === "cohost"
          }
          setTranslationDetails={setTranslationDetails}
          translationDetails={translationDetails}
          meetingFeatures={meetingFeatures}
          setLiveCapTourState={setLiveCapTourState}
          finalTranslatedCaptions={finalTranslatedCaptions}
          finalCaptions={finalcaptions}
        />
      }
      show={isLiveCaptionsDrawerOpen}
      setShow={setIsLiveCaptionsDrawerOpen}
      isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
      style={{ gap: "1rem" }}
    >
      {parseMetadata(localParticipant.metadata)?.role_name === "moderator" ||
      parseMetadata(localParticipant.metadata)?.role_name === "cohost" ? (
        livecaptionsobject.isLanguageSelected ? (
          <LiveCaptionsMessages
            isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
            liveCaptionData={liveCaptionData}
            meetingDetails={meetingDetails}
            localParticipant={localParticipant}
            remoteParticipants={remoteParticipants}
            finalCaptions={finalcaptions}
            setFinalCaptions={setfinalcaptions}
            langCode={livecaptionsobject.langCode}
            timeStamp={timeStamp}
            translationDetails={translationDetails}
            setToastNotification={setToastNotification}
            setToastStatus={setToastStatus}
            setShowToast={setShowToast}
            finalTranslatedCaptions={finalTranslatedCaptions}
            translateSelctedText={translateSelctedText}
            />
          ) : (
            <LanguageSelection
              handleLanguageSelection={handleLanguageSelection}
              setLiveCapTourState={setLiveCapTourState}
            />
          )
      ) : (
        <LiveCaptionsMessages
          isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
          liveCaptionData={liveCaptionData}
          meetingDetails={meetingDetails}
          localParticipant={localParticipant}
          remoteParticipants={remoteParticipants}
          finalCaptions={finalcaptions}
          setFinalCaptions={setfinalcaptions}
          langCode={livecaptionsobject.langCode}
          timeStamp={timeStamp}
          finalTranslatedCaptions={finalTranslatedCaptions}
          isHost={
            parseMetadata(localParticipant.metadata)?.role_name === "moderator"
          }
          isCoHost={
            parseMetadata(localParticipant.metadata)?.role_name === "cohost"
          }
          translationDetails={translationDetails}
          translateSelctedText={translateSelctedText}
          setToastNotification={setToastNotification}
          setToastStatus={setToastStatus}
          setShowToast={setShowToast}
        />
      )}
      <Joyride
        continuous // Keep the tour going until it's ended or closed
        run={liveCapTourState.run}
        showProgress={false} // Hide the progress
        showSkipButton
        stepIndex={liveCapTourState.stepIndex}
        callback={handleJoyrideCallback}
        steps={liveCapTourState.steps}
        styles={{
            options: {
                zIndex: 10000,
            },
            buttonBack: {
              color: "#3B60E4"
            },
            buttonNext: {
              backgroundColor: "#3B60E4",
            },
            buttonClose: {
              display: "none",
            },
            tooltipContent: {
              paddingBottom: "0px",
            }
        }}
        disableScrolling
        scrollDuration={1000}
        spotlightPadding={0}
        beaconComponent={false}
      />
    </SideDrawer>
  );
}
