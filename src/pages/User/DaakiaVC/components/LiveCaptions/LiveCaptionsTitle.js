// import { Popover } from "antd";
import React, { useEffect, useState } from "react";
import { Modal, Popconfirm, Popover } from "antd";
// import { ReactComponent as AudioIco } from "./Assets/Audio.svg";
// import { ReactComponent as VideoIco } from "./Assets/Video.svg";
// import { ReactComponent as LiveCapIco } from "./Assets/LiveCap.svg";
import { TfiClose } from "react-icons/tfi";
import { ReactComponent as LanguageSelectionIco } from "./Assets/Language.svg";
// import { ReactComponent as LanguageSelectionIco2 } from "./Assets/ic_translate_chats_blue.svg";
import { ReactComponent as LanguageSelectionIco2 } from "./Assets/ic_translate_chats_colored.svg";
import { ReactComponent as DownloadCaptions } from "./Assets/Download.svg";
// import { ReactComponent as LiveCapSettings } from "./Assets/Settings.svg";
import LanguageSelection from "./LanguageSelection";
import styles from "../../styles/ActionModal.module.scss";

export default function LiveCaptionsTitle({
  isLanguageSelected,
  handleLanguageSelection,
  isHost,
  isCoHost,
  setTranslationDetails,
  translationDetails,
  meetingFeatures,
  setLiveCapTourState,
  finalCaptions,
  finalTranslatedCaptions,
}) {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const popoverContent = <div>Choose Language</div>;
  const [isDownloadCaptionsModalOpen, setIsDownloadCaptionsModalOpen] =
    useState(false);
  const text = "Click here to choose your transcript language.";
  const [tip, setTip] = useState(translationDetails.target_lang === "");

  const handleSelectedLanguage = (langId, language) => {
    setIsModalVisible(false);
    if ((isHost || isCoHost) && !isLanguageSelected) {
      handleLanguageSelection(langId, language);
    } else {
      setTranslationDetails({
        ...translationDetails,
        target_lang: langId,
      });
    }
    // console.log(language);
  };

  // Download captions
  const downloadCaptions = () => {
    const textContent = finalCaptions
      .map((caption) => `${caption.name} ${caption.time}\n${caption.text}\n\n`)
      .join("");
    const blob = new Blob([textContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "captions.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadTranslatedCaptions = () => {
    const textContent = finalTranslatedCaptions
      .map((caption) => `${caption.name} ${caption.time}\n${caption.text}\n\n`)
      .join("");
    const blob = new Blob([textContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "translated-captions.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  useEffect(() => {
    if (translationDetails.target_lang !== "") {
      setTip(false);
    }
  }, [translationDetails.target_lang]);

  return (
    <div className="live-captions-title">
      {isLanguageSelected ? (
        <div className="live-captions-title-live">
          <h2>Live Captions</h2>
          {isCoHost || isHost ? (
            <div className="live-cap-settings-icon live-cap-settings-icon-host">
              <Popover
                content={popoverContent}
                overlayClassName="choose-live-cap"
                placement="bottomRight"
                align={{ offset: [15, 0] }}
                // trigger={["click"]}
              >
                <LanguageSelectionIco2
                  onClick={() => setIsModalVisible(true)}
                />
              </Popover>
              <Popover
                content={<div>Download Captions</div>}
                overlayClassName="choose-live-cap"
                placement="bottomRight"
                align={{ offset: [15, 0] }}
                // trigger={["click"]}
              >
                <DownloadCaptions
                  // onClick={downloadCaptions}
                  onClick={() => setIsDownloadCaptionsModalOpen(true)}
                />
              </Popover>
            </div>
          ) : (
            <div className="live-cap-settings-icon">
              {meetingFeatures?.voice_text_translation === 1 && (
                <>
                  <Popover
                    content={popoverContent}
                    overlayClassName="choose-live-cap"
                    placement="bottomRight"
                    align={{ offset: [15, 0] }}
                    // trigger={["click"]}
                  >
                    <LanguageSelectionIco2
                      onClick={() => {
                        setIsModalVisible(true);
                        setLiveCapTourState((prev) => ({
                          ...prev,
                          run: false,
                          stepIndex: 3,
                        })); // Stop tour temporarily
                        const waitForElement = setInterval(() => {
                          const liveCapEl = document.querySelector(
                            ".language-search-box"
                          );
                          if (liveCapEl) {
                            clearInterval(waitForElement);

                            // 🎯 Scroll to the element before showing the step
                            liveCapEl.scrollIntoView({
                              behavior: "smooth",
                              block: "center",
                            });

                            // ⏳ Small delay to ensure smooth transition
                            setTimeout(() => {
                              setLiveCapTourState((prev) => ({
                                ...prev,
                                run: true,
                              })); // Restart after ensuring visibility
                            }, 500);
                          }
                        }, 300);
                      }}
                    />
                  </Popover>
                  <Popconfirm
                    placement="bottomRight"
                    title={text}
                    overlayClassName="live-cap-settings-pop-confirm"
                    okText={
                      <TfiClose
                        style={{
                          width: "12px",
                          height: "12px",
                          color: "black",
                          backgroundColor: "white",
                        }}
                      />
                    }
                    okButtonProps={{
                      style: {
                        // display: "none",
                        backgroundColor: "white",
                        border: "none",
                        borderRadius: "50%",
                        width: "20px",
                        height: "20px",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        position: "absolute",
                        top: "0",
                        right: "0",
                        padding: "0",
                        boxShadow: "none",
                      },
                    }}
                    // showOk={false}
                    showCancel={false}
                    open={tip}
                    onConfirm={() => setTip(false)}
                    onCancel={() => setTip(true)}
                    align={{ offset: [17, 24] }}
                  >
                    {/* <IoMdCloseCircle className="live-cap-settings-pop-confirm-close" /> */}
                    {/* <Button>TL</Button> */}
                  </Popconfirm>
                </>
              )}
            </div>
          )}
          {/* {(isHost || isCoHost) && (
            <DownloadCaptions onClick={downloadCaptions} />
          )} */}
          <Modal
            title="Choose Language"
            open={isModalVisible}
            onOk={() => {
              setIsModalVisible(false);
            }}
            onCancel={() => {
              setIsModalVisible(false);
            }}
            className="live-cap-settings-modal"
            footer={null}
          >
            <LanguageSelection
              handleLanguageSelection={handleSelectedLanguage}
              className="live-cap-settings-modal-language-selection"
              setLiveCapTourState={setLiveCapTourState}
            />
          </Modal>
        </div>
      ) : (
        <>
          <LanguageSelectionIco />
          <p>Transcription Language</p>
        </>
      )}
      <Modal
        open={isDownloadCaptionsModalOpen}
        onCancel={() => setIsDownloadCaptionsModalOpen(false)}
        footer={null}
        className={`${styles.actionModal} download-captions-modal`}
      >
        <DownloadCaptions className="download-captions-modal-icon" />
        <div className="download-captions-modal-description">
          <h4>Download Captions</h4>
          <p>Get the original or translated captions from your meeting.</p>
        </div>
        <div className="download-captions-modal-buttons">
          <Popover
            content={
              finalCaptions.length === 0 ? (
                <div>No original captions available</div>
              ) : (
                <div>Download Original Captions</div>
              )
            }
            overlayClassName="choose-live-cap"
            placement="top"
            align={{ offset: [0, 7] }}
          >
            <button
              disabled={finalCaptions.length === 0}
              className={`${
                styles.actionModalButton
              } download-captions-modal-buttons-original ${
                finalCaptions.length === 0 ? "disabled-btn" : ""
              }`}
              onClick={downloadCaptions}
            >
              <p>Original</p>
            </button>
          </Popover>
          <Popover
            content={
              finalTranslatedCaptions.length === 0 ? (
                <div>No translated captions available</div>
              ) : (
                <div>Download Translated Captions</div>
              )
            }
            overlayClassName="choose-live-cap"
            placement="top"
            align={{ offset: [0, 7] }}
            // trigger={["click"]}
          >
            <button
              disabled={finalTranslatedCaptions.length === 0}
              className={`${
                styles.actionModalButton
              } download-captions-modal-buttons-translated ${
                finalTranslatedCaptions.length === 0 ? "disabled-btn" : ""
              }`}
              onClick={downloadTranslatedCaptions}
            >
              <p>Translated</p>
            </button>
          </Popover>
        </div>
      </Modal>
    </div>
  );
}
